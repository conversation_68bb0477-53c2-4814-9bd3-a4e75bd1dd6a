import { useParams } from 'react-router-dom';
import './ChannelPage.css';

const ChannelPage = () => {
  const { category, eventId, channelId, subChannelId } = useParams();

  // 确定当前显示的通道名称和标题
  const currentChannelName = subChannelId || channelId;
  const isSubChannel = !!subChannelId;

  return (
    <div className="channel-page">
      <div className="channel-page__header">
        <div className="breadcrumb">
          <span className="breadcrumb-item">{category}</span>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-item">{eventId}</span>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-item">{channelId}</span>
          {isSubChannel && (
            <>
              <span className="breadcrumb-separator">›</span>
              <span className="breadcrumb-item current">{subChannelId}</span>
            </>
          )}
          {!isSubChannel && (
            <span className="breadcrumb-item current">{channelId}</span>
          )}
        </div>
        <h2>{currentChannelName} {isSubChannel ? '子通道' : '通道'}详情</h2>
        <p>这里是{category}下{eventId}事件中{channelId}{isSubChannel ? `的${subChannelId}子通道` : '通道'}的详细监测内容。</p>
      </div>
      
      <div className="channel-page__content">
        <div className="channel-page__section">
          <h3>通道信息</h3>
          <div className="channel-info-grid">
            <div className="info-item">
              <span className="info-label">{isSubChannel ? '子通道ID:' : '通道ID:'}</span>
              <span className="info-value">{currentChannelName}</span>
            </div>
            {isSubChannel && (
              <div className="info-item">
                <span className="info-label">父通道:</span>
                <span className="info-value">{channelId}</span>
              </div>
            )}
            <div className="info-item">
              <span className="info-label">所属事件:</span>
              <span className="info-value">{eventId}</span>
            </div>
            <div className="info-item">
              <span className="info-label">类别:</span>
              <span className="info-value">{category}</span>
            </div>
            <div className="info-item">
              <span className="info-label">状态:</span>
              <span className="info-value status-active">在线</span>
            </div>
            <div className="info-item">
              <span className="info-label">数据类型:</span>
              <span className="info-value">波形数据</span>
            </div>
            <div className="info-item">
              <span className="info-label">采样率:</span>
              <span className="info-value">1 GHz</span>
            </div>
          </div>
        </div>

        <div className="channel-page__section">
          <h3>实时波形数据</h3>
          <div className="waveform-container">
            <div className="waveform-display">
              <div className="waveform-header">
                <span className="waveform-title">{currentChannelName} 波形显示</span>
                <div className="waveform-controls">
                  <button className="control-btn">暂停</button>
                  <button className="control-btn">重置</button>
                  <button className="control-btn">导出</button>
                </div>
              </div>
              <div className="waveform-chart">
                📊 实时波形图表区域
                <div className="waveform-placeholder">
                  <div className="wave-line"></div>
                  <div className="wave-line"></div>
                  <div className="wave-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="channel-page__section">
          <h3>数据统计</h3>
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">📈</div>
              <div className="stat-content">
                <div className="stat-value">1,247,892</div>
                <div className="stat-label">总数据点</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">⚡</div>
              <div className="stat-content">
                <div className="stat-value">2.3 GHz</div>
                <div className="stat-label">平均频率</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <div className="stat-value">99.8%</div>
                <div className="stat-label">数据完整性</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🔧</div>
              <div className="stat-content">
                <div className="stat-value">0.2%</div>
                <div className="stat-label">错误率</div>
              </div>
            </div>
          </div>
        </div>

        <div className="channel-page__section">
          <h3>历史数据记录</h3>
          <div className="history-table">
            <table>
              <thead>
                <tr>
                  <th>时间戳</th>
                  <th>数据值</th>
                  <th>状态</th>
                  <th>备注</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>2024-01-15 14:23:01</td>
                  <td>42.5</td>
                  <td><span className="status-normal">正常</span></td>
                  <td>数据正常</td>
                </tr>
                <tr>
                  <td>2024-01-15 14:23:02</td>
                  <td>38.2</td>
                  <td><span className="status-normal">正常</span></td>
                  <td>数据正常</td>
                </tr>
                <tr>
                  <td>2024-01-15 14:23:03</td>
                  <td>45.1</td>
                  <td><span className="status-normal">正常</span></td>
                  <td>数据正常</td>
                </tr>
                <tr>
                  <td>2024-01-15 14:23:04</td>
                  <td>41.8</td>
                  <td><span className="status-warning">警告</span></td>
                  <td>轻微延迟</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChannelPage;

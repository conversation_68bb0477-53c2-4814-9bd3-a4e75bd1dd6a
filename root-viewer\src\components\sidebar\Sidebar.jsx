import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './Sidebar.css';

const Sidebar = ({ activeItem, onItemClick, onBlankClick, sidebarData }) => {
  // 初始化时就设置所有项目为折叠状态，避免先展开后折叠的闪烁
  const [collapsedItems, setCollapsedItems] = useState(() => {
    const initialCollapsedItems = {};
    // 预设所有静态菜单项为折叠状态
    const staticMenuItems = [
      'CD-LPMT', 'WP-LPMT', 'SPMT-T/Q', 'TT', 'CD-T/Q', 'WP-T/Q',
      'MM(Multi-Message)', 'LOW_E', 'TQ_TAO_CD', 'TQ_TAO_TVT', 'TQ_TAO_WT', 'TQ_TAO_CD_FEC'
    ];

    staticMenuItems.forEach(itemId => {
      initialCollapsedItems[itemId] = true;
    });

    return initialCollapsedItems;
  });
  const navigate = useNavigate();
  const location = useLocation();

  // 静态菜单项定义
  const staticMenuItems = [
    { id: 'CD-LPMT', label: 'CD-LPMT'},
    { id: 'WP-LPMT', label: 'WP-LPMT'},
    { id: 'SPMT-T/Q', label: 'SPMT-T/Q'},
    { id: 'TT', label: 'TT'},
    { id: 'CD-T/Q', label: 'CD-T/Q'},
    { id: 'WP-T/Q', label: 'WP-T/Q'},
    { id: 'MM(Multi-Message)', label: 'MM(Multi-Message)'},
    { id: 'LOW_E', label: 'LOW_E'},
    { id: 'TQ_TAO_CD', label: 'TQ_TAO_CD'},
    { id: 'TQ_TAO_TVT', label: 'TQ_TAO_TVT'},
    { id: 'TQ_TAO_WT', label: 'TQ_TAO_WT'},
    { id: 'TQ_TAO_CD_FEC', label: 'TQ_TAO_CD_FEC'},
  ];

  // 根据API数据动态生成菜单项
  const menuItems = useMemo(() => {
    if (!sidebarData) {
      // 如果没有API数据，返回静态菜单项（都不可选中）
      return staticMenuItems.map(item => ({
        ...item,
        disabled: true,
        hasChildren: false,
        children: []
      }));
    }

    return staticMenuItems.map(staticItem => {
      const apiData = sidebarData[staticItem.id];

      if (!apiData) {
        // 如果API数据中没有这个项目，设为禁用状态
        return {
          ...staticItem,
          disabled: true,
          hasChildren: false,
          children: []
        };
      }

      // 如果API数据中有这个项目，生成子菜单
      const children = Object.keys(apiData).map(eventKey => {
        const eventData = apiData[eventKey];
        const eventChildren = Object.keys(eventData).map(channelKey => {
          const channelData = eventData[channelKey];

          // 检查channelData是否是对象且包含子通道数据
          if (channelData && typeof channelData === 'object' && !Array.isArray(channelData)) {
            // 如果channelData包含子通道（如channel-0, channel-1等），创建四级结构
            const subChannels = Object.keys(channelData);
            if (subChannels.length > 0 && subChannels.some(key => key.startsWith('channel-'))) {
              const subChannelChildren = subChannels.map(subChannelKey => ({
                id: `${staticItem.id}-${eventKey}-${channelKey}-${subChannelKey}`,
                label: subChannelKey,
                disabled: false,
                hasChildren: false,
                children: []
              }));

              return {
                id: `${staticItem.id}-${eventKey}-${channelKey}`,
                label: channelKey,
                hasChildren: true,
                children: subChannelChildren,
                disabled: false
              };
            }
          }

          // 如果没有子通道数据，保持原有的三级结构
          return {
            id: `${staticItem.id}-${eventKey}-${channelKey}`,
            label: channelKey,
            hasChildren: false,
            children: [],
            disabled: false
          };
        });

        return {
          id: `${staticItem.id}-${eventKey}`,
          label: eventKey,
          hasChildren: eventChildren.length > 0,
          children: eventChildren,
          disabled: false
        };
      });

      return {
        ...staticItem,
        disabled: false,
        hasChildren: children.length > 0,
        children: children
      };
    });
  }, [sidebarData]);

  // 当sidebarData变化时，更新包含子项的项目折叠状态
  useEffect(() => {
    if (sidebarData) {
      setCollapsedItems(prevCollapsedItems => {
        const newCollapsedItems = { ...prevCollapsedItems };
        let hasChanges = false;

        // 遍历所有菜单项，将包含子项的项目设置为折叠状态
        menuItems.forEach(item => {
          if (item.hasChildren) {
            // 主菜单项（如 CD-LPMT、WP-LPMT）默认折叠
            if (!(item.id in newCollapsedItems)) {
              newCollapsedItems[item.id] = true;
              hasChanges = true;
            }

            // 将所有子项（如 ev-0, ev-1 等）也设置为折叠状态
            item.children.forEach(child => {
              if (child.hasChildren && !(child.id in newCollapsedItems)) {
                newCollapsedItems[child.id] = true;
                hasChanges = true;
              }
            });
          }
        });

        // 只有在有变化时才返回新对象，避免不必要的重新渲染
        return hasChanges ? newCollapsedItems : prevCollapsedItems;
      });
    }
  }, [sidebarData, menuItems]);

  const handleToggleCollapse = (itemId) => {
    setCollapsedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // 处理路由导航
  const handleItemClick = (item) => {
    // 调用原有的点击处理函数，确保activeItem状态更新
    if (onItemClick) {
      onItemClick(item.id);
    }

    // 如果是子项（包含 - 的项目），进行路由导航
    if (item.id.includes('-')) {
      // 分割ID来确定层级
      const parts = item.id.split('-');

      if (parts.length >= 4) {
        // 四级子项，如 CD-LPMT-ev-0-waveform-1-channel-0
        const category = parts[0];
        const eventId = parts[1];
        const channelId = parts[2];
        const subChannelId = parts.slice(3).join('-'); // 处理可能包含多个-的情况
        navigate(`/${category}/${eventId}/${channelId}/${subChannelId}`);
      } else if (parts.length === 3) {
        // 三级子项，如 CD-LPMT-ev-0-waveform-1
        const category = parts[0];
        const eventId = parts[1];
        const channelId = parts[2];
        navigate(`/${category}/${eventId}/${channelId}`);
      } else if (parts.length === 2) {
        // 二级子项（第一级子节点），如 CD-LPMT-ev-0
        const category = parts[0];
        const eventId = parts[1];
        navigate(`/${category}/${eventId}`);
      }
    }
  };

  // 处理第一级子节点的点击（既要展开/折叠，又要导航和选中）
  const handleFirstLevelChildClick = (item) => {
    // 先调用 onItemClick 确保状态更新
    if (onItemClick) {
      onItemClick(item.id);
    }

    // 如果有子节点，切换展开/折叠状态
    if (item.hasChildren) {
      handleToggleCollapse(item.id);
    }

    // 如果是子项（包含 - 的项目），进行路由导航
    if (item.id.includes('-')) {
      // 分割ID来确定层级
      const parts = item.id.split('-');

      if (parts.length >= 4) {
        // 四级子项，如 CD-LPMT-ev-0-waveform-1-channel-0
        const category = parts[0];
        const eventId = parts[1];
        const channelId = parts[2];
        const subChannelId = parts.slice(3).join('-'); // 处理可能包含多个-的情况
        navigate(`/${category}/${eventId}/${channelId}/${subChannelId}`);
      } else if (parts.length === 3) {
        // 三级子项，如 CD-LPMT-ev-0-waveform-1
        const category = parts[0];
        const eventId = parts[1];
        const channelId = parts[2];
        navigate(`/${category}/${eventId}/${channelId}`);
      } else if (parts.length === 2) {
        // 二级子项（第一级子节点），如 CD-LPMT-ev-0
        const category = parts[0];
        const eventId = parts[1];
        navigate(`/${category}/${eventId}`);
      }
    }
  };

  // 递归渲染菜单项的函数
  const renderMenuItem = (item, level = 0) => {
    const isCollapsed = collapsedItems[item.id];
    // 只有当前项目被选中时才显示为活跃状态，不考虑子项
    // 确保只有被直接选中的项目才高亮，父级项目不会因为子项被选中而高亮
    const isActive = activeItem === item.id;
    const isDisabled = item.disabled;

    // 判断是否为第一级子节点（level=1且包含"-"）
    const isFirstLevelChild = level === 1 && item.id.includes('-');

    return (
      <li key={item.id} className={`sidebar__menu-item sidebar__menu-item--level-${level} ${isDisabled ? 'sidebar__menu-item--disabled' : ''}`}>
        {item.hasChildren ? (
          <>
            <button
              className={`sidebar__menu-link sidebar__menu-link--parent sidebar__menu-link--level-${level} ${
                isActive ? 'sidebar__menu-link--active' : ''
              } ${isDisabled ? 'sidebar__menu-link--disabled' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                if (!isDisabled) {
                  if (isFirstLevelChild) {
                    // 第一级子节点：既要展开/折叠，又要导航和选中
                    handleFirstLevelChildClick(item);
                  } else {
                    // 其他有子节点的项目（顶级菜单）：只展开/折叠，不选中
                    handleToggleCollapse(item.id);
                  }
                }
              }}
              title={item.label}
              disabled={isDisabled}
            >
              <span className="sidebar__menu-text">{item.label}</span>
              <span className={`sidebar__menu-arrow ${isCollapsed ? 'sidebar__menu-arrow--collapsed' : ''}`}>
                ▼
              </span>
            </button>
            {!isDisabled && (
              <ul className={`sidebar__submenu sidebar__submenu--level-${level} ${isCollapsed ? 'sidebar__submenu--collapsed' : ''}`}>
                {item.children.map((child) => renderMenuItem(child, level + 1))}
              </ul>
            )}
          </>
        ) : (
          <button
            className={`sidebar__menu-link sidebar__menu-link--level-${level} ${
              isActive ? 'sidebar__menu-link--active' : ''
            } ${isDisabled ? 'sidebar__menu-link--disabled' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              if (!isDisabled) {
                if (isFirstLevelChild) {
                  // 第一级子节点：导航和选中
                  handleFirstLevelChildClick(item);
                } else {
                  // 其他叶子节点：正常处理
                  handleItemClick(item);
                }
              }
            }}
            title={item.label}
            disabled={isDisabled}
          >
            <span className="sidebar__menu-text">{item.label}</span>
          </button>
        )}
      </li>
    );
  };

  // 处理点击空白区域
  const handleBlankAreaClick = (e) => {
    // 如果点击的是sidebar容器本身或nav区域（不是菜单项），则触发空白点击
    if (e.target.classList.contains('sidebar__nav') ||
        e.target.classList.contains('sidebar__menu') ||
        (e.target.closest('.sidebar') && !e.target.closest('.sidebar__menu-item'))) {
      if (onBlankClick) {
        onBlankClick();
      }
    }
  };

  return (
    <div className="sidebar sidebar--open" onClick={handleBlankAreaClick}>
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <span className="sidebar__logo-text">单事例监测系统</span>
        </div>
      </div>

      <nav className="sidebar__nav">
        <ul className="sidebar__menu">
          {menuItems.map((item) => renderMenuItem(item))}
        </ul>
      </nav>

      {/* <div className="sidebar__footer">
        <div className="sidebar__user">
          <div className="sidebar__user-avatar">👤</div>
          <div className="sidebar__user-info">
            <div className="sidebar__user-name">管理员</div>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;

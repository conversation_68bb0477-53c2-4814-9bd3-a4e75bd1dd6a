/**
 * API服务 - 处理与后端API的通信
 */

const API_BASE_URL = 'http://127.0.0.1:4523/m1/6925692-6641801-default';

// 测试数据 - 包含四级结构的示例数据
const testData = {
  "run_state": true,
  "error_message": "null",
  "total_size": "3MB",
  "data_assembled_id": "4",
  "event_number": 10,
  "if_compressed": false,
  "stream_tag": "global_trigger",
  "sidebar": {
    "CD-LPMT": {
      "ev-0": {
        "waveform-1": {
          "channel-0": "esse Lorem aute est",
          "channel-1": "adipisicing aliquip ut",
          "channel-17612": "Ut amet tempor"
        },
        "waveform-2": {
          "channel-0": "laborum labore eiusmod",
          "channel-17612": "do reprehenderit consectetur"
        },
        "hardware trigger1": {
          "channel-0": "cillum amet"
        }
      },
      "ev-1": {
        "waveform-3": {},
        "waveform-4": {},
        "hardware trigger2": {}
      },
      "ev-2": {},
      "ev-3": {},
      "ev-4": {},
      "ev-5": {},
      "ev-6": {}
    },
    "WP-LPMT": {
      "ev-7": {},
      "ev-8": {},
      "ev-9": {}
    }
  }
};

/**
 * 获取DP基础数据
 * @returns {Promise<Object>} API响应数据
 */
export const fetchDPBasicData = async () => {
  try {
    // 首先尝试从API获取数据
    const response = await fetch(`${API_BASE_URL}/message/DP_basic`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取DP基础数据失败，使用测试数据:', error);
    // 如果API调用失败，返回测试数据
    return new Promise((resolve) => {
      setTimeout(() => resolve(testData), 500); // 模拟网络延迟
    });
  }
};

/**
 * 格式化数据大小显示
 * @param {string} size - 数据大小字符串
 * @returns {string} 格式化后的大小
 */
export const formatDataSize = (size) => {
  if (!size) return '0MB';
  return size;
};

/**
 * 格式化布尔值显示
 * @param {boolean} value - 布尔值
 * @returns {string} 格式化后的字符串
 */
export const formatBooleanValue = (value) => {
  return value ? 'true' : 'false';
};

/**
 * 获取运行状态的CSS类名
 * @param {boolean} runState - 运行状态
 * @returns {string} CSS类名
 */
export const getRunStateClass = (runState) => {
  return runState ? 'system-status--normal' : 'system-status--abnormal';
};

/**
 * 获取运行状态的显示文本
 * @param {boolean} runState - 运行状态
 * @returns {string} 显示文本
 */
export const getRunStateText = (runState) => {
  return runState ? '运行状态' : '停止状态';
};
